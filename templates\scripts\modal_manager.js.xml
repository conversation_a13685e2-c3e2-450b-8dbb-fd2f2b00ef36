<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Modal Management JavaScript -->
    <t t-name="scripts.modal_manager">
        <script><![CDATA[
            // Modal Manager Class
            class ModalManager {
                constructor() {
                    this.activeModal = null;
                    this.initializeEventListeners();
                }

                initializeEventListeners() {
                    // Handle escape key to close modals
                    document.addEventListener('keydown', (e) => {
                        if (e.key === 'Escape' && this.activeModal) {
                            this.hideModal(this.activeModal);
                        }
                    });

                    // Handle backdrop clicks
                    document.addEventListener('click', (e) => {
                        if (e.target.classList.contains('modal-backdrop')) {
                            this.hideModal(e.target.id);
                        }
                    });
                }

                showModal(modalId) {
                    const modal = document.getElementById(modalId);
                    if (!modal) {
                        console.error(`Modal with id '${modalId}' not found`);
                        return;
                    }

                    // Hide any currently active modal
                    if (this.activeModal && this.activeModal !== modalId) {
                        this.hideModal(this.activeModal);
                    }

                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    this.activeModal = modalId;

                    // Focus first input if available
                    const firstInput = modal.querySelector('input, select, textarea');
                    if (firstInput) {
                        setTimeout(() => firstInput.focus(), 100);
                    }

                    // Prevent body scroll
                    document.body.style.overflow = 'hidden';
                }

                hideModal(modalId) {
                    const modal = document.getElementById(modalId);
                    if (!modal) {
                        console.error(`Modal with id '${modalId}' not found`);
                        return;
                    }

                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    
                    if (this.activeModal === modalId) {
                        this.activeModal = null;
                    }

                    // Reset form if it exists
                    const form = modal.querySelector('form');
                    if (form) {
                        form.reset();
                    }

                    // Restore body scroll
                    document.body.style.overflow = '';
                }

                isModalOpen(modalId) {
                    const modal = document.getElementById(modalId);
                    return modal && !modal.classList.contains('hidden');
                }
            }

            // Global modal manager instance
            const modalManager = new ModalManager();

            // Global modal functions for backward compatibility
            function showCreateDatabaseModal() {
                modalManager.showModal('createDatabaseModal');
            }

            function hideCreateDatabaseModal() {
                modalManager.hideModal('createDatabaseModal');
            }

            function showDatabaseInfoModal() {
                modalManager.showModal('databaseInfoModal');
            }

            function hideDatabaseInfoModal() {
                modalManager.hideModal('databaseInfoModal');
            }

            // Database menu toggle functionality
            function toggleDatabaseMenu(button) {
                const menu = button.nextElementSibling;
                const allMenus = document.querySelectorAll('[data-menu="true"]');
                
                // Close all other menus
                allMenus.forEach(m => {
                    if (m !== menu) {
                        m.classList.add('hidden');
                    }
                });
                
                // Toggle current menu
                menu.classList.toggle('hidden');
                
                // Close menu when clicking outside
                if (!menu.classList.contains('hidden')) {
                    const closeMenu = (e) => {
                        if (!button.contains(e.target) && !menu.contains(e.target)) {
                            menu.classList.add('hidden');
                            document.removeEventListener('click', closeMenu);
                        }
                    };
                    setTimeout(() => document.addEventListener('click', closeMenu), 0);
                }
            }

            // Show database information in modal
            function showDatabaseInfo(dbName) {
                const modal = document.getElementById('databaseInfoModal');
                const content = document.getElementById('databaseInfoContent');

                if (!modal || !content) {
                    console.error('Database info modal elements not found');
                    return;
                }

                // Show loading state
                content.innerHTML = `
                    <div class="text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading database information...</p>
                    </div>
                `;

                // Fetch detailed database information
                fetch(`/api/databases/${encodeURIComponent(dbName)}/info`)
                    .then(response => response.json())
                    .then(dbInfo => {
                        displayDatabaseInfo(dbInfo);
                    })
                    .catch(error => {
                        console.error('Error fetching database info:', error);
                        // Fallback to card data
                        const dbCard = document.querySelector(`[data-dbname="${dbName}"]`);
                        if (dbCard) {
                            const fallbackInfo = extractCardInfo(dbName, dbCard);
                            displayDatabaseInfo(fallbackInfo);
                        } else {
                            content.innerHTML = `
                                <div class="text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                                    <p>Failed to load database information</p>
                                </div>
                            `;
                        }
                    });

                modalManager.showModal('databaseInfoModal');
            }

            // Extract database info from card (fallback)
            function extractCardInfo(dbName, dbCard) {
                return {
                    name: dbName,
                    status: dbCard.querySelector('.bg-green-100, .bg-yellow-100') ?
                           (dbCard.querySelector('.bg-green-100') ? 'Ready' : 'Not Initialized') : 'Unknown',
                    created_display: 'Unknown',
                    size: 'Unknown',
                    table_count: '0',
                    active_connections: '0',
                    has_memory_registry: false
                };
            }

            // Display database information in modal
            function displayDatabaseInfo(dbInfo) {
                const content = document.getElementById('databaseInfoContent');
                if (!content) return;

                // Populate modal content with comprehensive information
                content.innerHTML = `
                    <div class="space-y-6">
                        <!-- Header -->
                        <div class="flex items-center space-x-3 pb-4 border-b border-gray-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-database text-white text-lg"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-gray-900">${dbInfo.name}</h4>
                                <p class="text-sm text-gray-500">Database Information & Statistics</p>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h5 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                Basic Information
                            </h5>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Status:</span>
                                    <span class="font-medium ${dbInfo.status === 'Ready' ? 'text-green-600' : 'text-yellow-600'}">${dbInfo.status || 'Unknown'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Created:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.created_display || dbInfo.created || 'Unknown'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Size:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.size || 'Unknown'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Owner:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.owner || 'erp'}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Database Statistics -->
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h5 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                                Database Statistics
                            </h5>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Tables:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.table_count || '0'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Active Connections:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.active_connections || '0'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Encoding:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.encoding || 'UTF8'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Connection Limit:</span>
                                    <span class="font-medium text-gray-900">${dbInfo.connection_limit === -1 ? 'Unlimited' : (dbInfo.connection_limit || 'Unknown')}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Memory Registry Information -->
                        ${dbInfo.has_memory_registry ? `
                            <div class="bg-green-50 rounded-lg p-4">
                                <h5 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                    <i class="fas fa-memory text-green-500 mr-2"></i>
                                    Memory Registry
                                </h5>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Status:</span>
                                        <span class="font-medium text-green-600">Active</span>
                                    </div>
                                    ${dbInfo.registry_info ? `
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Modules:</span>
                                            <span class="font-medium text-gray-900">${dbInfo.registry_info.installed_modules_count || '0'}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Routes:</span>
                                            <span class="font-medium text-gray-900">${dbInfo.registry_info.routes_count || '0'}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Environments:</span>
                                            <span class="font-medium text-gray-900">${dbInfo.registry_info.active_environments || '0'}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        ` : `
                            <div class="bg-yellow-50 rounded-lg p-4">
                                <h5 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                    Memory Registry
                                </h5>
                                <p class="text-sm text-yellow-700">No memory registry found for this database.</p>
                            </div>
                        `}

                        <!-- Action Buttons -->
                        <div class="flex space-x-3 pt-4 border-t border-gray-200">
                            <button type="button"
                                    class="flex-1 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 border border-transparent rounded-md hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 shadow-sm hover:shadow-md"
                                    onclick="hideDatabaseInfoModal(); databaseOps.connectToDatabase('${dbInfo.name}')">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Connect to Database
                            </button>
                            <button type="button"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                    onclick="hideDatabaseInfoModal()">
                                Close
                            </button>
                        </div>
                    </div>
                `;
            }
        ]]></script>
    </t>
</templates>
