<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Modular Database List Enhanced Template -->
    <t t-name="database_list_enhanced_modular">
        <t t-call="base_layout">
            <t t-set="title" t-value="'Database Management - ERP System'"/>
            <t t-set="header_title" t-value="'Database Management'"/>
            <t t-set="header_subtitle" t-value="'Manage your ERP databases'"/>
            
            <!-- Header Actions -->
            <t t-set="header_actions">
                <button class="inline-flex items-center px-2 sm:px-3 py-1 sm:py-1.5 border border-gray-300 rounded-md text-xs sm:text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors"
                        onclick="databaseOps.refreshDatabases()">
                    <i class="fas fa-sync-alt mr-1 sm:mr-1.5 text-xs"></i>
                    <span class="hidden sm:inline">Refresh</span>
                </button>
                <button class="inline-flex items-center px-2 sm:px-3 py-1 sm:py-1.5 rounded-md text-xs sm:text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors" 
                        onclick="showCreateDatabaseModal()">
                    <i class="fas fa-plus mr-1 sm:mr-1.5 text-xs"></i>
                    <span class="hidden sm:inline">Create Database</span>
                </button>
            </t>

            <!-- Additional Styles -->
            <t t-set="additional_styles">
                <t t-call="styles.custom_css"/>
            </t>

            <!-- Main Content -->
            <t t-set="content">
                <!-- Status Bar -->
                <t t-call="components.status_bar">
                    <t t-set="config" t-value="config"/>
                    <t t-set="databases" t-value="databases"/>
                </t>

                <!-- System Statistics Dashboard -->
                <t t-call="components.system_statistics">
                    <t t-set="config" t-value="config"/>
                    <t t-set="databases" t-value="databases"/>
                </t>

                <!-- Database List -->
                <div id="database-list">
                    <t t-if="databases and len(databases) > 0">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4 mb-4 sm:mb-6">
                            <t t-foreach="databases" t-as="db">
                                <t t-call="components.database_card">
                                    <t t-set="db" t-value="db"/>
                                </t>
                            </t>
                        </div>
                    </t>

                    <!-- Empty State -->
                    <t t-if="not databases or len(databases) == 0">
                        <t t-call="components.empty_state"/>
                    </t>
                </div>

                <!-- Modals -->
                <t t-call="components.create_database_modal"/>
                <t t-call="components.database_info_modal"/>
            </t>

            <!-- Scripts -->
            <t t-set="additional_scripts">
                <!-- Core JavaScript Modules -->
                <t t-call="scripts.utils"/>
                <t t-call="scripts.modal_manager"/>
                <t t-call="scripts.database_operations"/>
                <t t-call="scripts.search_filter"/>
                
                <!-- Page Initialization -->
                <script><![CDATA[
                    // Page initialization
                    document.addEventListener('DOMContentLoaded', function() {
                        console.log('Database List Enhanced (Modular) - Initialized');
                        
                        // Initialize components
                        if (window.searchManager) {
                            window.searchManager.setupSearch();
                        }

                        // Update last updated time
                        const updateTime = () => {
                            const lastUpdated = document.getElementById('last-updated');
                            if (lastUpdated) {
                                lastUpdated.textContent = 'Just now';
                            }
                        };
                        updateTime();

                        // Initialize system statistics
                        if (window.databaseOps) {
                            window.databaseOps.updateSystemStatistics();
                        }
                        
                        // Show keyboard shortcuts hint
                        console.log('Keyboard shortcuts:');
                        console.log('- Ctrl/Cmd + K: Focus search');
                        console.log('- Ctrl/Cmd + N: Create new database');
                        console.log('- Escape: Close modals/clear search');
                        
                        // Add accessibility improvements
                        const cards = document.querySelectorAll('[data-dbname]');
                        cards.forEach(card => {
                            card.setAttribute('role', 'button');
                            card.setAttribute('tabindex', '0');
                            
                            // Add keyboard navigation
                            card.addEventListener('keydown', function(e) {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    card.click();
                                }
                            });
                        });
                        
                        // Add focus management for modals
                        const modals = document.querySelectorAll('[id$="Modal"]');
                        modals.forEach(modal => {
                            modal.setAttribute('role', 'dialog');
                            modal.setAttribute('aria-modal', 'true');
                        });
                    });
                    
                    // Error handling
                    window.addEventListener('error', function(e) {
                        console.error('Page error:', e.error);
                        if (window.Utils && window.Utils.showToast) {
                            window.Utils.showToast('An error occurred. Please refresh the page.', 'error');
                        }
                    });
                    
                    // Handle unhandled promise rejections
                    window.addEventListener('unhandledrejection', function(e) {
                        console.error('Unhandled promise rejection:', e.reason);
                        if (window.Utils && window.Utils.showToast) {
                            window.Utils.showToast('A network error occurred. Please try again.', 'error');
                        }
                    });

                    // Global functions for system statistics
                    window.refreshSystemStats = function() {
                        if (window.databaseOps) {
                            window.databaseOps.refreshSystemStats();
                        }
                    };

                    window.exportSystemReport = function() {
                        if (window.databaseOps) {
                            window.databaseOps.exportSystemReport();
                        }
                    };
                ]]></script>
            </t>
        </t>
    </t>
</templates>
